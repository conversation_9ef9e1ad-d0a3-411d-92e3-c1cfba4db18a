#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DJI设备状态上报接口
负责在设备连接、拔除、文件上传完成等关键事件时上报状态信息
"""

import logging
import time
import json
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any


class DeviceEventType(Enum):
    """设备事件类型枚举"""
    DEVICE_CONNECTED = "device_connected"      # 设备连接
    DEVICE_DISCONNECTED = "device_disconnected"  # 设备拔除
    UPLOAD_COMPLETED = "upload_completed"      # 文件上传完成
    UPLOAD_FAILED = "upload_failed"           # 文件上传失败
    BACKUP_COMPLETED = "backup_completed"     # 备份完成
    BACKUP_FAILED = "backup_failed"          # 备份失败


class DeviceStatusReporter:
    """DJI设备状态上报器"""
    
    def __init__(self, logger=None):
        """
        初始化状态上报器
        
        Args:
            logger: 日志记录器，如果为None则创建新的记录器
        """
        self.logger = logger or self._create_logger()
        self.logger.info("DJI设备状态上报器初始化完成")
    
    def _create_logger(self):
        """创建专用的日志记录器"""
        logger = logging.getLogger('DJI_StatusReporter')
        logger.setLevel(logging.INFO)
        
        # 如果没有处理器，添加控制台处理器
        if not logger.handlers:
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def _generate_report_data(self, event_type: DeviceEventType, device_sn: str, 
                            additional_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成上报数据
        
        Args:
            event_type: 事件类型
            device_sn: 设备序列号
            additional_data: 额外的数据
            
        Returns:
            格式化的上报数据
        """
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type.value,
            "device_sn": device_sn,
            "report_time": int(time.time())
        }
        
        if additional_data:
            report_data.update(additional_data)
        
        return report_data
    
    def report_device_connected(self, device_sn: str, device_info: Optional[Dict[str, Any]] = None):
        """
        上报设备连接事件
        
        Args:
            device_sn: 设备序列号
            device_info: 设备信息（可选）
        """
        try:
            additional_data = {
                "message": "DJI设备已连接",
                "device_info": device_info or {}
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.DEVICE_CONNECTED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.info(f"[设备连接] 飞机SN: {device_sn}")
            self.logger.info(f"[设备连接] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报设备连接事件失败: {e}")
    
    def report_device_disconnected(self, device_sn: str, disconnect_reason: Optional[str] = None):
        """
        上报设备拔除事件
        
        Args:
            device_sn: 设备序列号
            disconnect_reason: 拔除原因（可选）
        """
        try:
            additional_data = {
                "message": "DJI设备已拔除",
                "disconnect_reason": disconnect_reason or "用户主动拔除"
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.DEVICE_DISCONNECTED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.info(f"[设备拔除] 飞机SN: {device_sn}")
            self.logger.info(f"[设备拔除] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报设备拔除事件失败: {e}")
    
    def report_upload_completed(self, device_sn: str, upload_stats: Optional[Dict[str, Any]] = None):
        """
        上报文件上传完成事件
        
        Args:
            device_sn: 设备序列号
            upload_stats: 上传统计信息（可选）
        """
        try:
            additional_data = {
                "message": "文件上传完成",
                "upload_stats": upload_stats or {}
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.UPLOAD_COMPLETED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.info(f"[上传完成] 飞机SN: {device_sn}")
            if upload_stats:
                self.logger.info(f"[上传完成] 统计信息: 文件数={upload_stats.get('file_count', 0)}, "
                               f"总大小={upload_stats.get('total_size', 0)}, "
                               f"耗时={upload_stats.get('duration', 0)}秒")
            self.logger.info(f"[上传完成] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报文件上传完成事件失败: {e}")
    
    def report_upload_failed(self, device_sn: str, error_info: Optional[Dict[str, Any]] = None):
        """
        上报文件上传失败事件
        
        Args:
            device_sn: 设备序列号
            error_info: 错误信息（可选）
        """
        try:
            additional_data = {
                "message": "文件上传失败",
                "error_info": error_info or {}
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.UPLOAD_FAILED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.error(f"[上传失败] 飞机SN: {device_sn}")
            if error_info:
                self.logger.error(f"[上传失败] 错误信息: {error_info.get('error_message', '未知错误')}")
            self.logger.error(f"[上传失败] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报文件上传失败事件失败: {e}")
    
    def report_backup_completed(self, device_sn: str, backup_info: Optional[Dict[str, Any]] = None):
        """
        上报备份完成事件
        
        Args:
            device_sn: 设备序列号
            backup_info: 备份信息（可选）
        """
        try:
            additional_data = {
                "message": "设备数据备份完成",
                "backup_info": backup_info or {}
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.BACKUP_COMPLETED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.info(f"[备份完成] 飞机SN: {device_sn}")
            if backup_info:
                self.logger.info(f"[备份完成] 备份信息: 路径={backup_info.get('backup_path', '')}, "
                               f"文件数={backup_info.get('file_count', 0)}, "
                               f"大小={backup_info.get('total_size', 0)}")
            self.logger.info(f"[备份完成] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报备份完成事件失败: {e}")
    
    def report_backup_failed(self, device_sn: str, error_info: Optional[Dict[str, Any]] = None):
        """
        上报备份失败事件
        
        Args:
            device_sn: 设备序列号
            error_info: 错误信息（可选）
        """
        try:
            additional_data = {
                "message": "设备数据备份失败",
                "error_info": error_info or {}
            }
            
            report_data = self._generate_report_data(
                DeviceEventType.BACKUP_FAILED, 
                device_sn, 
                additional_data
            )
            
            # 记录到日志
            self.logger.error(f"[备份失败] 飞机SN: {device_sn}")
            if error_info:
                self.logger.error(f"[备份失败] 错误信息: {error_info.get('error_message', '未知错误')}")
            self.logger.error(f"[备份失败] 上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
            
            # TODO: 在这里添加实际的API调用逻辑
            # self._send_to_api(report_data)
            
        except Exception as e:
            self.logger.error(f"上报备份失败事件失败: {e}")
    
    def _send_to_api(self, report_data: Dict[str, Any]):
        """
        发送数据到API接口（预留接口）
        
        Args:
            report_data: 要发送的数据
        """
        # TODO: 实现实际的API调用逻辑
        # 例如：
        # import requests
        # response = requests.post(API_URL, json=report_data, timeout=30)
        # if response.status_code == 200:
        #     self.logger.info("状态上报成功")
        # else:
        #     self.logger.error(f"状态上报失败: {response.status_code}")
        
        self.logger.debug(f"[API调用] 预留接口，数据: {json.dumps(report_data, ensure_ascii=False)}")


# 创建全局实例
device_reporter = DeviceStatusReporter()


def get_device_reporter(logger=None):
    """
    获取设备状态上报器实例
    
    Args:
        logger: 可选的日志记录器
        
    Returns:
        DeviceStatusReporter实例
    """
    if logger:
        return DeviceStatusReporter(logger)
    return device_reporter


if __name__ == "__main__":
    # 测试代码
    reporter = DeviceStatusReporter()
    
    # 测试各种事件上报
    test_device_sn = "DJI_TEST_123456"
    
    print("测试设备状态上报功能...")
    
    # 测试设备连接
    reporter.report_device_connected(test_device_sn, {
        "device_type": "DJI Mini 3 Pro",
        "firmware_version": "01.00.0300"
    })
    
    # 测试备份完成
    reporter.report_backup_completed(test_device_sn, {
        "backup_path": "/home/<USER>/DJI_TEST_123456_20250627_123456",
        "file_count": 25,
        "total_size": 1024 * 1024 * 500  # 500MB
    })
    
    # 测试上传完成
    reporter.report_upload_completed(test_device_sn, {
        "file_count": 25,
        "total_size": 1024 * 1024 * 500,
        "duration": 120,
        "cloud_provider": "qiniu"
    })
    
    # 测试设备拔除
    reporter.report_device_disconnected(test_device_sn)
    
    print("测试完成！")
