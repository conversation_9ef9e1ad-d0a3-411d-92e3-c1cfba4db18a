# DJI设备状态上报器使用说明

## 概述

设备状态上报器是DJI监控服务的重要组成部分，负责在关键事件发生时上报设备状态信息和飞机序列号。目前支持以下事件的上报：

1. **设备连接** - DJI设备插入时
2. **设备拔除** - DJI设备拔出时  
3. **备份完成** - 设备数据备份完成时
4. **备份失败** - 设备数据备份失败时
5. **上传完成** - 文件上传到云存储完成时
6. **上传失败** - 文件上传到云存储失败时

## 功能特性

- ✅ **多事件支持** - 支持6种不同的设备事件类型
- ✅ **结构化数据** - 使用JSON格式记录详细的状态信息
- ✅ **飞机序列号追踪** - 每个事件都包含设备的唯一序列号
- ✅ **线程安全** - 支持多线程并发调用
- ✅ **错误处理** - 完善的异常处理机制
- ✅ **可扩展接口** - 预留API调用接口，便于后续扩展
- ✅ **中文支持** - 完整支持中文字符和数据

## 文件结构

```
dji_monitor_service/
├── device_status_reporter.py      # 状态上报器主文件
├── dji_monitor.py                 # 主监控服务（已集成状态上报）
├── test_status_reporter.py        # 状态上报器测试脚本
├── test_integration.py            # 集成测试脚本
└── 设备状态上报器使用说明.md      # 本文档
```

## 使用方法

### 1. 基本使用

```python
from device_status_reporter import DeviceStatusReporter

# 创建状态上报器实例
reporter = DeviceStatusReporter()

# 上报设备连接事件
reporter.report_device_connected("DJI_MINI3PRO_123456", {
    'device_path': '/dev/sdb1',
    'vendor': 'DJI Technology Co., Ltd.',
    'model': 'DJI Mini 3 Pro'
})

# 上报设备拔除事件
reporter.report_device_disconnected("DJI_MINI3PRO_123456")
```

### 2. 使用自定义日志记录器

```python
import logging
from device_status_reporter import DeviceStatusReporter

# 创建自定义日志记录器
logger = logging.getLogger('MyApp')
logger.setLevel(logging.INFO)

# 创建状态上报器实例
reporter = DeviceStatusReporter(logger)
```

### 3. 使用全局实例

```python
from device_status_reporter import get_device_reporter

# 获取全局状态上报器实例
reporter = get_device_reporter()

# 或者使用自定义日志记录器
reporter = get_device_reporter(my_logger)
```

## 事件类型详解

### 1. 设备连接事件

```python
reporter.report_device_connected(device_sn, device_info)
```

**参数说明：**
- `device_sn`: 设备序列号（必需）
- `device_info`: 设备信息字典（可选）

**示例输出：**
```json
{
  "timestamp": "2025-06-27T05:44:33.301189",
  "event_type": "device_connected",
  "device_sn": "DJI_MINI3PRO_123456789",
  "report_time": 1751003073,
  "message": "DJI设备已连接",
  "device_info": {
    "device_path": "/dev/sdb1",
    "vendor": "DJI Technology Co., Ltd.",
    "model": "DJI Mini 3 Pro"
  }
}
```

### 2. 设备拔除事件

```python
reporter.report_device_disconnected(device_sn, disconnect_reason)
```

**参数说明：**
- `device_sn`: 设备序列号（必需）
- `disconnect_reason`: 拔除原因（可选，默认"用户主动拔除"）

### 3. 备份完成事件

```python
reporter.report_backup_completed(device_sn, backup_info)
```

**参数说明：**
- `device_sn`: 设备序列号（必需）
- `backup_info`: 备份信息字典（可选）

**建议的backup_info字段：**
- `backup_path`: 备份目录路径
- `file_count`: 备份文件数量
- `total_size`: 备份总大小（字节）
- `formatted_size`: 格式化的大小字符串

### 4. 上传完成事件

```python
reporter.report_upload_completed(device_sn, upload_stats)
```

**参数说明：**
- `device_sn`: 设备序列号（必需）
- `upload_stats`: 上传统计信息字典（可选）

**建议的upload_stats字段：**
- `file_count`: 成功上传文件数
- `total_files`: 总文件数
- `uploaded_size`: 上传大小（字节）
- `total_size`: 总大小（字节）
- `cloud_provider`: 云存储提供商
- `duration`: 上传耗时（秒）

## 在主程序中的集成

状态上报器已经完全集成到DJI监控服务中，会在以下时机自动触发：

1. **设备连接时** - 在`process_dji_device()`方法中，获取设备信息后
2. **设备拔除时** - 在`handle_device_remove()`方法中，获取设备信息后
3. **备份完成时** - 在`backup_dji_data()`方法中，备份成功后
4. **上传完成时** - 在七牛云和阿里云OSS上传完成后

## 测试和验证

### 1. 运行状态上报器测试

```bash
python3 test_status_reporter.py
```

### 2. 运行集成测试

```bash
python3 test_integration.py
```

### 3. 测试覆盖范围

- ✅ 基本功能测试
- ✅ 错误处理测试
- ✅ JSON序列化测试
- ✅ 中文数据测试
- ✅ 复杂数据结构测试
- ✅ 全局实例测试
- ✅ 主程序集成测试

## 日志输出示例

```
2025-06-27 05:44:33,301 - DJI_StatusReporter - INFO - [设备连接] 飞机SN: DJI_MINI3PRO_123456789
2025-06-27 05:44:34,302 - DJI_StatusReporter - INFO - [备份完成] 飞机SN: DJI_MINI3PRO_123456789
2025-06-27 05:44:34,302 - DJI_StatusReporter - INFO - [备份完成] 备份信息: 路径=/home/<USER>/backup, 文件数=25, 大小=524288000
2025-06-27 05:44:35,304 - DJI_StatusReporter - INFO - [上传完成] 飞机SN: DJI_MINI3PRO_123456789
2025-06-27 05:44:35,304 - DJI_StatusReporter - INFO - [上传完成] 统计信息: 文件数=25, 总大小=524288000, 耗时=120秒
2025-06-27 05:44:36,306 - DJI_StatusReporter - INFO - [设备拔除] 飞机SN: DJI_MINI3PRO_123456789
```

## 扩展API接口

状态上报器预留了`_send_to_api()`方法，可以轻松扩展为实际的API调用：

```python
def _send_to_api(self, report_data: Dict[str, Any]):
    """发送数据到API接口"""
    import requests
    
    API_URL = "https://your-api-endpoint.com/device-status"
    
    try:
        response = requests.post(
            API_URL, 
            json=report_data, 
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            self.logger.info("状态上报成功")
        else:
            self.logger.error(f"状态上报失败: {response.status_code}")
            
    except Exception as e:
        self.logger.error(f"API调用异常: {e}")
```

## 注意事项

1. **线程安全** - 状态上报器是线程安全的，可以在多线程环境中使用
2. **异常处理** - 所有上报方法都包含异常处理，不会影响主程序运行
3. **性能影响** - 状态上报是轻量级操作，对主程序性能影响极小
4. **日志级别** - 成功事件使用INFO级别，失败事件使用ERROR级别
5. **数据格式** - 所有数据都使用UTF-8编码的JSON格式

## 故障排除

如果状态上报器不工作，请检查：

1. **导入问题** - 确保`device_status_reporter.py`在正确路径
2. **日志配置** - 检查日志级别是否正确设置
3. **权限问题** - 确保有写入日志文件的权限
4. **内存不足** - 在资源受限环境中可能需要调整日志缓冲

## 更新历史

- **v1.0** (2025-06-27) - 初始版本，支持6种事件类型的状态上报
