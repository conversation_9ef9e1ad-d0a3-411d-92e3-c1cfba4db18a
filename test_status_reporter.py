#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备状态上报器功能
"""

import sys
import os
import time
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from device_status_reporter import DeviceStatusReporter, get_device_reporter


def setup_test_logger():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('TestStatusReporter')


def test_device_status_reporter():
    """测试设备状态上报器的各种功能"""
    print("=" * 60)
    print("测试设备状态上报器功能")
    print("=" * 60)
    
    # 创建测试日志记录器
    logger = setup_test_logger()
    
    # 创建状态上报器实例
    print("\n1. 创建状态上报器实例...")
    reporter = DeviceStatusReporter(logger)
    
    # 测试设备序列号
    test_device_sn = "DJI_MINI3PRO_123456789"
    
    print(f"\n2. 测试设备连接事件上报...")
    print(f"   设备序列号: {test_device_sn}")
    reporter.report_device_connected(test_device_sn, {
        'device_path': '/dev/sdb1',
        'vendor': 'DJI Technology Co., Ltd.',
        'model': 'DJI Mini 3 Pro',
        'device_id': 'sdb1',
        'firmware_version': '01.00.0300'
    })
    
    time.sleep(1)
    
    print(f"\n3. 测试备份完成事件上报...")
    reporter.report_backup_completed(test_device_sn, {
        'backup_path': '/home/<USER>/DJI_MINI3PRO_123456789_20250627_123456_abcd1234',
        'file_count': 25,
        'total_size': 1024 * 1024 * 500,  # 500MB
        'formatted_size': '500.0 MB'
    })
    
    time.sleep(1)
    
    print(f"\n4. 测试上传完成事件上报...")
    reporter.report_upload_completed(test_device_sn, {
        'file_count': 25,
        'total_files': 25,
        'uploaded_size': 1024 * 1024 * 500,
        'total_size': 1024 * 1024 * 500,
        'cloud_provider': 'aliyun_oss',
        'failed_count': 0,
        'duration': 120
    })
    
    time.sleep(1)
    
    print(f"\n5. 测试设备拔除事件上报...")
    reporter.report_device_disconnected(test_device_sn, "用户主动拔除")
    
    time.sleep(1)
    
    print(f"\n6. 测试上传失败事件上报...")
    reporter.report_upload_failed(test_device_sn, {
        'error_message': '网络连接超时',
        'failed_count': 5,
        'total_files': 25,
        'cloud_provider': 'aliyun_oss'
    })
    
    time.sleep(1)
    
    print(f"\n7. 测试备份失败事件上报...")
    reporter.report_backup_failed(test_device_sn, {
        'error_message': '磁盘空间不足',
        'backup_path': '/home/<USER>/backup'
    })
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)


def test_global_reporter():
    """测试全局状态上报器实例"""
    print("\n" + "=" * 60)
    print("测试全局状态上报器实例")
    print("=" * 60)
    
    # 使用全局实例
    reporter = get_device_reporter()
    
    test_device_sn = "DJI_GLOBAL_TEST_987654321"
    
    print(f"\n使用全局实例测试设备连接...")
    reporter.report_device_connected(test_device_sn, {
        'device_type': 'DJI Air 2S',
        'test_mode': True
    })
    
    print(f"\n使用全局实例测试设备拔除...")
    reporter.report_device_disconnected(test_device_sn)
    
    print("\n全局实例测试完成！")


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理")
    print("=" * 60)
    
    reporter = DeviceStatusReporter()
    
    # 测试空设备序列号
    print("\n测试空设备序列号...")
    try:
        reporter.report_device_connected("", {})
        print("✓ 空设备序列号处理正常")
    except Exception as e:
        print(f"✗ 空设备序列号处理异常: {e}")
    
    # 测试None参数
    print("\n测试None参数...")
    try:
        reporter.report_upload_completed("TEST_SN", None)
        print("✓ None参数处理正常")
    except Exception as e:
        print(f"✗ None参数处理异常: {e}")
    
    print("\n错误处理测试完成！")


def test_json_serialization():
    """测试JSON序列化"""
    print("\n" + "=" * 60)
    print("测试JSON序列化")
    print("=" * 60)
    
    reporter = DeviceStatusReporter()
    
    # 测试包含中文的数据
    print("\n测试中文数据序列化...")
    try:
        reporter.report_device_connected("测试设备_123", {
            'device_name': '大疆无人机',
            'location': '北京市',
            'operator': '张三'
        })
        print("✓ 中文数据序列化正常")
    except Exception as e:
        print(f"✗ 中文数据序列化异常: {e}")
    
    # 测试复杂数据结构
    print("\n测试复杂数据结构...")
    try:
        complex_data = {
            'nested_dict': {
                'level1': {
                    'level2': ['item1', 'item2', 'item3']
                }
            },
            'numbers': [1, 2, 3.14, 1024 * 1024],
            'boolean': True,
            'null_value': None
        }
        reporter.report_backup_completed("COMPLEX_TEST", complex_data)
        print("✓ 复杂数据结构序列化正常")
    except Exception as e:
        print(f"✗ 复杂数据结构序列化异常: {e}")
    
    print("\nJSON序列化测试完成！")


if __name__ == "__main__":
    try:
        # 运行所有测试
        test_device_status_reporter()
        test_global_reporter()
        test_error_handling()
        test_json_serialization()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！设备状态上报器功能正常！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
