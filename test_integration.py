#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DJI监控服务与状态上报器的集成
"""

import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    print("=" * 60)
    print("测试导入功能")
    print("=" * 60)
    
    try:
        print("1. 测试设备状态上报器导入...")
        from device_status_reporter import DeviceStatusReporter, get_device_reporter
        print("✓ 设备状态上报器导入成功")
        
        print("2. 测试云存储配置导入...")
        from cloud_storage_config import CLOUD_STORAGE_PROVIDER, CLOUD_UPLOAD_ENABLED
        print(f"✓ 云存储配置导入成功，当前提供商: {CLOUD_STORAGE_PROVIDER}")
        
        print("3. 测试阿里云OSS上传器导入...")
        from aliyun_oss_uploader import AliyunOSSUploader
        print("✓ 阿里云OSS上传器导入成功")
        
        print("4. 测试主程序导入...")
        # 这里只测试导入，不实际运行
        import dji_monitor
        print("✓ 主程序导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_status_reporter_integration():
    """测试状态上报器与主程序的集成"""
    print("\n" + "=" * 60)
    print("测试状态上报器集成")
    print("=" * 60)
    
    try:
        # 创建一个简化的日志记录器
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger('IntegrationTest')
        
        print("1. 创建状态上报器实例...")
        from device_status_reporter import DeviceStatusReporter
        reporter = DeviceStatusReporter(logger)
        print("✓ 状态上报器创建成功")
        
        print("2. 测试状态上报器基本功能...")
        test_sn = "INTEGRATION_TEST_123"
        reporter.report_device_connected(test_sn, {'test': 'integration'})
        reporter.report_device_disconnected(test_sn)
        print("✓ 状态上报器基本功能正常")
        
        print("3. 测试阿里云OSS上传器与状态上报器集成...")
        from aliyun_oss_uploader import AliyunOSSUploader
        from cloud_storage_config import ALIYUN_OSS_CONFIG
        
        # 创建OSS上传器实例（带状态上报器）
        oss_uploader = AliyunOSSUploader(ALIYUN_OSS_CONFIG, logger, reporter)
        print("✓ 阿里云OSS上传器与状态上报器集成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration():
    """测试配置文件"""
    print("\n" + "=" * 60)
    print("测试配置文件")
    print("=" * 60)
    
    try:
        from cloud_storage_config import (
            CLOUD_STORAGE_PROVIDER, 
            CLOUD_UPLOAD_ENABLED,
            ALIYUN_OSS_CONFIG
        )
        
        print(f"1. 云存储提供商: {CLOUD_STORAGE_PROVIDER}")
        print(f"2. 云存储上传状态: {'启用' if CLOUD_UPLOAD_ENABLED else '禁用'}")
        print(f"3. 阿里云OSS配置检查...")
        
        required_keys = ['access_key_id', 'access_key_secret', 'endpoint', 'bucket_name']
        for key in required_keys:
            if key in ALIYUN_OSS_CONFIG:
                print(f"   ✓ {key}: 已配置")
            else:
                print(f"   ❌ {key}: 未配置")
        
        print("✓ 配置文件检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_main_program_initialization():
    """测试主程序初始化（不启动监控）"""
    print("\n" + "=" * 60)
    print("测试主程序初始化")
    print("=" * 60)
    
    try:
        print("1. 导入主程序模块...")
        from dji_monitor import DJIDeviceMonitor
        
        print("2. 创建监控器实例（测试模式）...")
        # 使用临时日志文件
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.log', delete=False) as tmp_log:
            log_file = tmp_log.name
        
        monitor = DJIDeviceMonitor(log_file=log_file)
        print("✓ 监控器实例创建成功")
        
        print("3. 检查状态上报器初始化...")
        if hasattr(monitor, 'status_reporter') and monitor.status_reporter:
            print("✓ 状态上报器已正确初始化")
        else:
            print("⚠️ 状态上报器未初始化或不可用")
        
        print("4. 检查云存储配置...")
        print(f"   云存储提供商: {monitor.cloud_provider}")
        print(f"   云存储上传状态: {'启用' if monitor.cloud_upload_enabled else '禁用'}")
        
        if monitor.cloud_uploader:
            print("✓ 云存储上传器已初始化")
        else:
            print("⚠️ 云存储上传器未初始化")
        
        # 清理
        monitor.executor.shutdown(wait=False)
        monitor.upload_executor.shutdown(wait=False)
        os.unlink(log_file)
        
        print("✓ 主程序初始化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 主程序初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有集成测试"""
    print("🚀 开始DJI监控服务集成测试")
    print("=" * 60)
    
    tests = [
        ("导入功能", test_imports),
        ("状态上报器集成", test_status_reporter_integration),
        ("配置文件", test_configuration),
        ("主程序初始化", test_main_program_initialization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！DJI监控服务已准备就绪！")
        print("\n📋 功能清单:")
        print("   ✓ 设备状态上报器")
        print("   ✓ 多云存储支持（七牛云 + 阿里云OSS）")
        print("   ✓ 设备连接/拔除事件上报")
        print("   ✓ 备份完成/失败事件上报")
        print("   ✓ 上传完成/失败事件上报")
        print("   ✓ 线程安全的并发处理")
        print("   ✓ 配置文件管理")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
